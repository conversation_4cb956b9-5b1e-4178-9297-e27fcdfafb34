﻿
namespace Demo.Common;

public class Demo
{
    public void Test()
    {

    }
}


public class User
{
    public long Id { get; set; }

    public string Name { get; set; }
}

public class Reader
{
    private User user;

    public IBooks Books { get; set; }

    public interface IBooks : IHasMany<long, Book>
    {
    }
}

public class ReaderBooks : DbEntityList<long, Book>, Reader.IBooks
{
    private long readerId;

    public ReaderBooks()
    {
    }

    public override Book? GetById(long id)
    {
        return new Book();
    }

    public override List<Book> GetList()
    {
        return [];
    }
}

public class Book : Entity<long>
{
    public long Id { get; set; }

    public string Title { get; set; }


    public override long GetId()
    {
        return Id;
    }
}