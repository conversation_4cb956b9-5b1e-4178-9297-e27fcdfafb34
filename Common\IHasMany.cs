﻿namespace Demo.Common;

public interface IHasMany<ID, E>
    where E : Entity<ID>
{
    List<E> GetList();

    E? GetById(ID id);
}


public class MemoryEntityList<ID, E> : IHasMany<ID, E>
    where E : Entity<ID>
{
    private readonly List<E> _list;

    public MemoryEntityList()
    {
        _list = [];
    }

    public MemoryEntityList(List<E> list)
    {
        _list = list;
    }

    public int Count()
    {
        return _list.Count;
    }

    public List<E> GetList()
    {
        return _list;
    }

    public E? GetById(ID id)
    {
        return _list.FirstOrDefault(x => x.GetId()!.Equals(id));
    }
}


public abstract class DbEntityList<ID, E> : IHasMany<ID, E>
    where E : Entity<ID>
{
    public abstract List<E> GetList();

    public abstract E? GetById(ID id);
}










public abstract class Entity<ID>
{
    public abstract ID GetId();
}