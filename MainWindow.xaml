<Window x:Class="Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="iPhone Style Button Demo" Height="600" Width="400"
        Background="#F2F2F7">
    
    <Window.Resources>
        <!-- iPhone Style Button Template -->
        <Style x:Key="iPhoneButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#007AFF" Offset="0"/>
                        <GradientStop Color="#0051D5" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="MinHeight" Value="44"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#1E88E5" Offset="0"/>
                                            <GradientStop Color="#1565C0" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#0D47A1" Offset="0"/>
                                            <GradientStop Color="#1565C0" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="#C7C7CC"/>
                                <Setter Property="Foreground" Value="#8E8E93"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Secondary iPhone Button Style -->
        <Style x:Key="iPhoneSecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource iPhoneButtonStyle}">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#007AFF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#007AFF"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#BBDEFB"/>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Destructive iPhone Button Style -->
        <Style x:Key="iPhoneDestructiveButtonStyle" TargetType="Button" BasedOn="{StaticResource iPhoneButtonStyle}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#FF3B30" Offset="0"/>
                        <GradientStop Color="#D70015" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Width="300">
            <TextBlock Text="iPhone Style Buttons" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,30"
                       Foreground="#1C1C1E"/>
            
            <!-- Primary Button -->
            <Button Content="Primary Button" 
                    Style="{StaticResource iPhoneButtonStyle}"
                    Click="PrimaryButton_Click"/>
            
            <!-- Secondary Button -->
            <Button Content="Secondary Button" 
                    Style="{StaticResource iPhoneSecondaryButtonStyle}"
                    Click="SecondaryButton_Click"/>
            
            <!-- Destructive Button -->
            <Button Content="Delete" 
                    Style="{StaticResource iPhoneDestructiveButtonStyle}"
                    Click="DestructiveButton_Click"/>
            
            <!-- Disabled Button -->
            <Button Content="Disabled Button" 
                    Style="{StaticResource iPhoneButtonStyle}"
                    IsEnabled="False"/>
            
            <!-- Result Display -->
            <TextBlock x:Name="ResultTextBlock" 
                       Text="Click a button to see the result"
                       FontSize="14"
                       HorizontalAlignment="Center"
                       Margin="0,30,0,0"
                       Foreground="#8E8E93"
                       TextWrapping="Wrap"/>
        </StackPanel>
    </Grid>
</Window>
