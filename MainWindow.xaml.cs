using System.Windows;

namespace Demo
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void PrimaryButton_Click(object sender, RoutedEventArgs e)
        {
            ResultTextBlock.Text = "Primary button was clicked!";
        }

        private void SecondaryButton_Click(object sender, RoutedEventArgs e)
        {
            ResultTextBlock.Text = "Secondary button was clicked!";
        }

        private void DestructiveButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to delete?", 
                                       "Confirm Delete", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                ResultTextBlock.Text = "Delete confirmed!";
            }
            else
            {
                ResultTextBlock.Text = "Delete cancelled.";
            }
        }
    }
}
