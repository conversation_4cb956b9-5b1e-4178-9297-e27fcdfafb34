﻿namespace Demo.MeetingMng
{
    class Printer
    {
        public string Id { get; set; } = string.Empty;

        public List<SignupSheet> SignupSheets { get; set; } = [];

        public void Print()
        {
            foreach (SignupSheet sheet in SignupSheets)
            {
                while (sheet.TryDequeueUnprintedInfo(out var info))
                {
                    info.IsPrinted = true;

                    //TODO: 持久化

                    Console.WriteLine(info.Name);
                    //Thread.Sleep(100);
                    //Task.Delay(100);
                }
            }
        }
    }
}
