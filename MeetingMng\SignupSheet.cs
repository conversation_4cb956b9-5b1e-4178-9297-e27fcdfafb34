﻿namespace Demo.MeetingMng
{
    class SignupSheet
    {
        object locker = new();

        Meeting Meeting { get; set; }

        string DeviceNo { get; set; }

        List<SignupInfo> SignupList { get; set; } = [];

        Queue<SignupInfo> UnprintedQueue { get; set; } = [];


        public SignupSheet(Meeting meeting, string deviceNo)
        {
            Meeting = meeting;
            DeviceNo = deviceNo;
        }


        public void Signup(SignupInfo info)
        {
            //lock (locker)
            {
                info.MeetingId = Meeting.Id;
                info.DeviceNo = DeviceNo;
                SignupList.Add(info);

                //TODO: 持久化

                if (!info.IsPrinted)
                    UnprintedQueue.Enqueue(info);
            }
        }

        public bool TryDequeueUnprintedInfo(out SignupInfo info)
        {
            //lock (locker)
            {
                return UnprintedQueue.TryDequeue(out info!);
            }
        }

        public List<SignupInfo> GetSignupList()
        {
            return SignupList;
        }
    }
}
