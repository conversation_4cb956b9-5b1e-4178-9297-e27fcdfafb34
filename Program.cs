﻿
// See https://aka.ms/new-console-template for more information

//using Microsoft.Extensions.AI;

//OllamaChatClient client = new("http://localhost:11434", "deepseek-r1:8b");

//var chatMessages = new List<ChatMessage> {
//    new(ChatRole.User, "Hello"),
//    new(ChatRole.User, "1 + 1 = ?")
//};

//var reply = await client.CompleteAsync(chatMessages);

//Console.WriteLine(reply.Message);

//Console.ReadLine();


/*
 * Printer Demo
 */

using Demo.MeetingMng;

//Meeting meeting = new Meeting { Id = 1 };
//SignupSheet sheet = new SignupSheet(meeting, "a");
//meeting.SignupSheets.Add(sheet);

//for (int i = 0; i < 100; i++)
//{
//    sheet.Signup(new SignupInfo { Name = $"张{i}", SignupTime = DateTime.Now });
//}

//Printer printerA1 = new();
//printerA1.SignupSheets.Add(sheet);
//Printer printerA2 = new();
//printerA2.SignupSheets.Add(sheet);

//var t1 = Task.Run(() => { printerA1.Print(); });
//var t2 = Task.Run(() => { printerA2.Print(); });

//for (int i = 100; i < 200; i++)
//{
//    sheet.Signup(new SignupInfo { Name = $"张{i}", SignupTime = DateTime.Now });
//}

//Task.WaitAll(t1, t2);




Test(0);
Test(1);
Test(2);


Console.ReadLine();



static void Test(int c)
{
    Console.WriteLine($"间隔天数{c}");
    for (int i = 0; i < 10; i++)
    {
        Console.WriteLine($"第{i + 1}天日期：{DateTime.Now.AddDays(i + (i * c)).ToString("yyyy-MM-dd")}");
    }
}
