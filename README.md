# iPhone Style Button Demo - WPF Application

这是一个展示iPhone样式按钮的WPF应用程序。

## 功能特性

### iPhone样式按钮包含以下特性：

1. **主要按钮 (Primary Button)**
   - 蓝色渐变背景 (#007AFF 到 #0051D5)
   - 白色文字
   - 圆角边框 (8px)
   - 悬停和按下效果
   - 按下时轻微缩放动画

2. **次要按钮 (Secondary Button)**
   - 透明背景，蓝色边框
   - 蓝色文字
   - 悬停时浅蓝色背景
   - 相同的圆角和动画效果

3. **危险按钮 (Destructive Button)**
   - 红色渐变背景 (#FF3B30 到 #D70015)
   - 白色文字
   - 点击时显示确认对话框

4. **禁用按钮**
   - 灰色背景和文字
   - 无交互效果

## 技术实现

### 项目结构
```
Demo/
├── App.xaml                 # WPF应用程序定义
├── App.xaml.cs             # 应用程序代码后台
├── MainWindow.xaml         # 主窗口界面定义
├── MainWindow.xaml.cs      # 主窗口代码后台
├── Demo.csproj            # 项目文件
└── README.md              # 说明文档
```

### 关键技术点

1. **WPF样式 (Styles)**
   - 使用 `Style` 定义按钮外观
   - 通过 `ControlTemplate` 自定义按钮模板
   - 使用 `Trigger` 实现交互效果

2. **渐变背景**
   - `LinearGradientBrush` 创建渐变效果
   - 不同状态使用不同的渐变色

3. **动画效果**
   - `ScaleTransform` 实现按下缩放效果
   - `Trigger` 控制状态变化

4. **圆角边框**
   - `Border` 控件的 `CornerRadius` 属性

## 运行应用程序

1. 确保安装了 .NET 8.0 SDK
2. 在项目目录中运行：
   ```bash
   dotnet build
   dotnet run
   ```

## 按钮样式使用方法

在XAML中使用定义好的样式：

```xml
<!-- 主要按钮 -->
<Button Content="Primary Button" 
        Style="{StaticResource iPhoneButtonStyle}"/>

<!-- 次要按钮 -->
<Button Content="Secondary Button" 
        Style="{StaticResource iPhoneSecondaryButtonStyle}"/>

<!-- 危险按钮 -->
<Button Content="Delete" 
        Style="{StaticResource iPhoneDestructiveButtonStyle}"/>
```

## 自定义和扩展

可以通过修改 `MainWindow.xaml` 中的样式定义来自定义：
- 颜色方案
- 圆角大小
- 动画效果
- 字体样式

这个实现完全模拟了iOS系统中的按钮设计规范，提供了现代化的用户界面体验。
