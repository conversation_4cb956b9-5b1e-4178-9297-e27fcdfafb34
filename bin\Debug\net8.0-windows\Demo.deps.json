{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Demo/1.0.0": {"dependencies": {"Microsoft.Extensions.AI.Ollama": "9.1.0-preview.1.25064.3", "Microsoft.KernelMemory.AI.Ollama": "0.96.250120.1"}, "runtime": {"Demo.dll": {}}}, "Google.Protobuf/3.27.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.27.1.0", "fileVersion": "3.27.1.0"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.56604"}}}, "Microsoft.Extensions.AI.Abstractions/9.1.0-preview.1.25064.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "9.1.0.0", "fileVersion": "9.100.25.6403"}}}, "Microsoft.Extensions.AI.Ollama/9.1.0-preview.1.25064.3": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.1.0-preview.1.25064.3", "System.Net.Http.Json": "8.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.Ollama.dll": {"assemblyVersion": "9.1.0.0", "fileVersion": "9.100.25.6403"}}}, "Microsoft.Extensions.Configuration/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Diagnostics/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "System.Diagnostics.DiagnosticSource": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileSystemGlobbing": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Hosting/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.Configuration.CommandLine": "9.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Diagnostics": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1", "Microsoft.Extensions.Hosting.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Configuration": "9.0.1", "Microsoft.Extensions.Logging.Console": "9.0.1", "Microsoft.Extensions.Logging.Debug": "9.0.1", "Microsoft.Extensions.Logging.EventLog": "9.0.1", "Microsoft.Extensions.Logging.EventSource": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "System.Diagnostics.DiagnosticSource": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Console/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Configuration": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Debug/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "System.Diagnostics.EventLog": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Primitives/9.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24523.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.KernelMemory.Abstractions/0.96.250120.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.Extensions.Hosting": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.SemanticKernel.Abstractions": "1.33.0", "System.Linq.Async": "6.0.1", "System.Memory.Data": "9.0.1", "System.Numerics.Tensors": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Ollama/0.96.250120.1": {"dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.96.250120.1", "Microsoft.KernelMemory.Abstractions": "0.96.250120.1", "OllamaSharp": "4.0.22"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Ollama.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Tiktoken/0.96.250120.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.96.250120.1", "Microsoft.ML.Tokenizers.Data.Cl100kBase": "1.0.1", "Microsoft.ML.Tokenizers.Data.O200kBase": "1.0.1", "Microsoft.ML.Tokenizers.Data.P50kBase": "1.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Tiktoken.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.ML.Tokenizers/1.0.1": {"dependencies": {"Google.Protobuf": "3.27.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.ML.Tokenizers.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.125.6404"}}}, "Microsoft.ML.Tokenizers.Data.Cl100kBase/1.0.1": {"dependencies": {"Microsoft.ML.Tokenizers": "1.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.Cl100kBase.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.125.6404"}}}, "Microsoft.ML.Tokenizers.Data.O200kBase/1.0.1": {"dependencies": {"Microsoft.ML.Tokenizers": "1.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.O200kBase.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.125.6404"}}}, "Microsoft.ML.Tokenizers.Data.P50kBase/1.0.1": {"dependencies": {"Microsoft.ML.Tokenizers": "1.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.P50kBase.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.125.6404"}}}, "Microsoft.SemanticKernel.Abstractions/1.33.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI.Abstractions": "9.1.0-preview.1.25064.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.VectorData.Abstractions": "9.0.0-preview.1.24523.1", "System.Diagnostics.DiagnosticSource": "9.0.1", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "1.33.0.0", "fileVersion": "1.33.0.0"}}}, "OllamaSharp/4.0.22": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Extensions.AI.Abstractions": "9.1.0-preview.1.25064.3"}, "runtime": {"lib/net8.0/OllamaSharp.dll": {"assemblyVersion": "4.0.22.0", "fileVersion": "4.0.22.0"}}}, "System.Diagnostics.DiagnosticSource/9.0.1": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Diagnostics.EventLog/9.0.1": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.IO.Pipelines/9.0.1": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Linq.Async/6.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.0"}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.35981"}}}, "System.Memory.Data/9.0.1": {"dependencies": {"System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Net.Http.Json/8.0.1": {}, "System.Numerics.Tensors/9.0.1": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Text.Encodings.Web/9.0.1": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "System.Text.Json/9.0.1": {"dependencies": {"System.IO.Pipelines": "9.0.1", "System.Text.Encodings.Web": "9.0.1"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}}}, "libraries": {"Demo/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.27.1": {"type": "package", "serviceable": true, "sha512": "sha512-7IVz9TzhYCZ8qY0rPhXUnyJSXYdshUqmmxmTI763XmDDSJJFnyfKH43FFcMJu/CZgBcE98xlFztrKwhzcRkiPg==", "path": "google.protobuf/3.27.1", "hashPath": "google.protobuf.3.27.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-owmu2Cr3IQ8yQiBleBHlGk8dSQ12oaF2e7TpzwJKEl4m84kkZJjEY1n33L67Y3zM5jPOjmmbdHjbfiL0RqcMRQ==", "path": "microsoft.bcl.asyncinterfaces/9.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.1.0-preview.1.25064.3": {"type": "package", "serviceable": true, "sha512": "sha512-qxGgo8ipnJz9viMQBMrnmYymi9cZWV0kep/wNTHgLhieYBCVvkMnOskaoNzRjCMl9hwEkXnZTQIwYk2WbpmmWA==", "path": "microsoft.extensions.ai.abstractions/9.1.0-preview.1.25064.3", "hashPath": "microsoft.extensions.ai.abstractions.9.1.0-preview.1.25064.3.nupkg.sha512"}, "Microsoft.Extensions.AI.Ollama/9.1.0-preview.1.25064.3": {"type": "package", "serviceable": true, "sha512": "sha512-/0xF8C2xtmKvm1ls94sBV+N4jFWjmW9A/hKUT78tCbzN6dnuL/mjP8FNWVOoXM8OX8Uz/JVSSU6heWAFLv8B/w==", "path": "microsoft.extensions.ai.ollama/9.1.0-preview.1.25064.3", "hashPath": "microsoft.extensions.ai.ollama.9.1.0-preview.1.25064.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VuthqFS+ju6vT8W4wevdhEFiRi1trvQtkzWLonApfF5USVzzDcTBoY3F24WvN/tffLSrycArVfX1bThm/9xY2A==", "path": "microsoft.extensions.configuration/9.0.1", "hashPath": "microsoft.extensions.configuration.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+4hfFIY1UjBCXFTTOd+ojlDPq6mep3h5Vq5SYE3Pjucr7dNXmq4S/6P/LoVnZFz2e/5gWp/om4svUFgznfULcA==", "path": "microsoft.extensions.configuration.abstractions/9.0.1", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w7kAyu1Mm7eParRV6WvGNNwA8flPTub16fwH49h7b/yqJZFTgYxnOVCuiah3G2bgseJMEq4DLjjsyQRvsdzRgA==", "path": "microsoft.extensions.configuration.binder/9.0.1", "hashPath": "microsoft.extensions.configuration.binder.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5WC1OsXfljC1KHEyL0yefpAyt1UZjrZ0/xyOqFowc5VntbE79JpCYOTSYFlxEuXm3Oq5xsgU2YXeZLTgAAX+DA==", "path": "microsoft.extensions.configuration.commandline/9.0.1", "hashPath": "microsoft.extensions.configuration.commandline.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5HShUdF8KFAUSzoEu0DOFbX09FlcFtHxEalowyjM7Kji0EjdF0DLjHajb2IBvoqsExAYox+Z2GfbfGF7dH7lKQ==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QBOI8YVAyKqeshYOyxSe6co22oag431vxMu5xQe1EjXMkYE4xK4J71xLCW3/bWKmr9Aoy1VqGUARSLFnotk4Bg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-z+g+lgPET1JRDjsOkFe51rkkNcnJgvOK5UIpeTfF1iAi0GkBJz5/yUuTa8a9V8HUh4gj4xFT5WGoMoXoSDKfGg==", "path": "microsoft.extensions.configuration.json/9.0.1", "hashPath": "microsoft.extensions.configuration.json.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-esGPOgLZ1tZddEomexhrU+LJ5YIsuJdkh0tU7r4WVpNZ15dLuMPqPW4Xe4txf3T2PDUX2ILe3nYQEDjZjfSEJg==", "path": "microsoft.extensions.configuration.usersecrets/9.0.1", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "path": "microsoft.extensions.dependencyinjection/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tr74eP0oQ3AyC24ch17N8PuEkrPbD0JqIfENCYqmgKYNOmL8wQKzLJu3ObxTUDrjnn4rHoR1qKa37/eQyHmCDA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4ZmP6turxMFsNwK/MCko2fuIITaYYN/eXyyIRq1FjLDKnptdbn6xMb7u0zfSMzCGpzkx4RxH/g1jKN2IchG7uA==", "path": "microsoft.extensions.diagnostics/9.0.1", "hashPath": "microsoft.extensions.diagnostics.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pfAPuVtHvG6dvZtAa0OQbXdDqq6epnr8z0/IIUjdmV0tMeI8Aj9KxDXvdDvqr+qNHTkmA7pZpChNxwNZt4GXVg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.1", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-DguZYt1DWL05+8QKWL3b6bW7A2pC5kYFMY5iXM6W2M23jhvcNa8v6AU8PvVJBcysxHwr9/jax0agnwoBumsSwg==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TKDMNRS66UTMEVT38/tU9hA63UTMvzI3DyNm5mx8+JCf3BaOtxgrvWLCI1y3J52PzT5yNl/T2KN5Z0KbApLZcg==", "path": "microsoft.extensions.fileproviders.physical/9.0.1", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mxcp9NXuQMvAnudRZcgIb5SqlWrlullQzntBLTwuv0MPIJ5LqiGwbRqiyxgdk+vtCoUkplb0oXy5kAw1t469Ug==", "path": "microsoft.extensions.filesystemglobbing/9.0.1", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-3wZNcVvC8RW44HDqqmIq+BqF5pgmTQdbNdR9NyYw33JSMnJuclwoJ2PEkrJ/KvD1U/hmqHVL3l5If+Hn3D1fWA==", "path": "microsoft.extensions.hosting/9.0.1", "hashPath": "microsoft.extensions.hosting.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CwSMhLNe8HLkfbFzdz0CHWJhtWH3TtfZSicLBd/itFD+NqQtfGHmvqXHQbaFFl3mQB5PBb2gxwzWQyW2pIj7PA==", "path": "microsoft.extensions.hosting.abstractions/9.0.1", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E/k5r7S44DOW+08xQPnNbO8DKAQHhkspDboTThNJ6Z3/QBb4LC6gStNWzVmy3IvW7sUD+iJKf4fj0xEkqE7vnQ==", "path": "microsoft.extensions.logging/9.0.1", "hashPath": "microsoft.extensions.logging.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w2gUqXN/jNIuvqYwX3lbXagsizVNXYyt6LlF57+tMve4JYCEgCMMAjRce6uKcDASJgpMbErRT1PfHy2OhbkqEA==", "path": "microsoft.extensions.logging.abstractions/9.0.1", "hashPath": "microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MeZePlyu3/74Wk4FHYSzXijADJUhWa7gxtaphLxhS8zEPWdJuBCrPo0sezdCSZaKCL+cZLSLobrb7xt2zHOxZQ==", "path": "microsoft.extensions.logging.configuration/9.0.1", "hashPath": "microsoft.extensions.logging.configuration.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-YUzguHYlWfp4upfYlpVe3dnY59P25wc+/YLJ9/NQcblT3EvAB1CObQulClll7NtnFbbx4Js0a0UfyS8SbRsWXQ==", "path": "microsoft.extensions.logging.console/9.0.1", "hashPath": "microsoft.extensions.logging.console.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pzdyibIV8k4sym0Sszcp2MJCuXrpOGs9qfOvY+hCRu8k4HbdVoeKOLnacxHK6vEPITX5o5FjjsZW2zScLXTjYA==", "path": "microsoft.extensions.logging.debug/9.0.1", "hashPath": "microsoft.extensions.logging.debug.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+a4RlbwFWjsMujNNhf1Jy9Nm5CpMT+nxXxfgrkRSloPo0OAWhPSPsrFo6VWpvgIPPS41qmfAVWr3DqAmOoVZgQ==", "path": "microsoft.extensions.logging.eventlog/9.0.1", "hashPath": "microsoft.extensions.logging.eventlog.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d47ZRZUOg1dGOX+yisWScQ7w4+92OlR9beS2UXaiadUCA3RFoZzobzVgrzBX7Oo/qefx9LxdRcaeFpWKb3BNBw==", "path": "microsoft.extensions.logging.eventsource/9.0.1", "hashPath": "microsoft.extensions.logging.eventsource.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nggoNKnWcsBIAaOWHA+53XZWrslC7aGeok+aR+epDPRy7HI7GwMnGZE8yEsL2Onw7kMOHVHwKcsDls1INkNUJQ==", "path": "microsoft.extensions.options/9.0.1", "hashPath": "microsoft.extensions.options.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-8RRKWtuU4fR+8MQLR/8CqZwZ9yc2xCpllw/WPRY7kskIqEq0hMcEI4AfUJO72yGiK2QJkrsDcUvgB5Yc+3+lyg==", "path": "microsoft.extensions.options.configurationextensions/9.0.1", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bHtTesA4lrSGD1ZUaMIx6frU3wyy0vYtTa/hM6gGQu5QNrydObv8T5COiGUWsisflAfmsaFOe9Xvw5NSO99z0g==", "path": "microsoft.extensions.primitives/9.0.1", "hashPath": "microsoft.extensions.primitives.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.24523.1": {"type": "package", "serviceable": true, "sha512": "sha512-5TUbyFZN8v2eWQXwF7WyAAHCW+OMdsmEVEWw1N1J7L5xA25UD0BtUXxO1kc8Nzkc6c1YGN/NwarySgiY84ALlg==", "path": "microsoft.extensions.vectordata.abstractions/9.0.0-preview.1.24523.1", "hashPath": "microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.24523.1.nupkg.sha512"}, "Microsoft.KernelMemory.Abstractions/0.96.250120.1": {"type": "package", "serviceable": true, "sha512": "sha512-hIUMfbGYJKR4jP08bJ9GDuIx/SFfF2Z2WtSe/fdqlU6xbGzQ1GjnvCKgl+gEKLCojyQTFe4aW4og59eTqVzFPA==", "path": "microsoft.kernelmemory.abstractions/0.96.250120.1", "hashPath": "microsoft.kernelmemory.abstractions.0.96.250120.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Ollama/0.96.250120.1": {"type": "package", "serviceable": true, "sha512": "sha512-knfOjovJCcuEIm0ET2/tI1uhueD6WeBoG11UV1odRjHA3zBzidAlPoYztt4vga+gpWieKDw5xznfkAz///2/tw==", "path": "microsoft.kernelmemory.ai.ollama/0.96.250120.1", "hashPath": "microsoft.kernelmemory.ai.ollama.0.96.250120.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Tiktoken/0.96.250120.1": {"type": "package", "serviceable": true, "sha512": "sha512-BueoC/p048TZnTVC+l1bBtlKrQ6assrBX2P5aygnDX7h9EbSkOQd6apHv93Geg7y4dd5MbEpq2W8y08CA0biBA==", "path": "microsoft.kernelmemory.ai.tiktoken/0.96.250120.1", "hashPath": "microsoft.kernelmemory.ai.tiktoken.0.96.250120.1.nupkg.sha512"}, "Microsoft.ML.Tokenizers/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9d6wyh+OSMbzh52OdBQDf9+g7yqr99bBGTACkFBorkvkgmrNoIDIVIjdGjCnAdDGLWFhD4W8L0vGZjUWQgIqjg==", "path": "microsoft.ml.tokenizers/1.0.1", "hashPath": "microsoft.ml.tokenizers.1.0.1.nupkg.sha512"}, "Microsoft.ML.Tokenizers.Data.Cl100kBase/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZOzZ9EGZNOu/h/YCOy5dD7v5ezMPHehj0SRKQEb1ZETQ8dL3LyzB1yGqywPlhmkW38qsc/3CXZr96habNb9v2w==", "path": "microsoft.ml.tokenizers.data.cl100kbase/1.0.1", "hashPath": "microsoft.ml.tokenizers.data.cl100kbase.1.0.1.nupkg.sha512"}, "Microsoft.ML.Tokenizers.Data.O200kBase/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vdIdI8ZVU6NPgbeLIHr59jcwkIEOFYEpBAyJKDeSM4K4UqzMU6QhPv0kCz2DtvE/Il0Ap4chTeKKV5ETV00wgQ==", "path": "microsoft.ml.tokenizers.data.o200kbase/1.0.1", "hashPath": "microsoft.ml.tokenizers.data.o200kbase.1.0.1.nupkg.sha512"}, "Microsoft.ML.Tokenizers.Data.P50kBase/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+Uka7+j4jIWEqHBSRlPm1jotobkU26dqukic8ATJrQQ0yyFvCr6nUoKQ2piQM+3mIsmdtLIIQVTMjEbNFn2+Tg==", "path": "microsoft.ml.tokenizers.data.p50kbase/1.0.1", "hashPath": "microsoft.ml.tokenizers.data.p50kbase.1.0.1.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.33.0": {"type": "package", "serviceable": true, "sha512": "sha512-Fl6kw1gpXdhVpui0c88rQA+sQT4Bsjdk51klMavH4KvxPP2yOcqsHCkhXMBDlBMJppChFBTt1KdSW3jxF440xg==", "path": "microsoft.semantickernel.abstractions/1.33.0", "hashPath": "microsoft.semantickernel.abstractions.1.33.0.nupkg.sha512"}, "OllamaSharp/4.0.22": {"type": "package", "serviceable": true, "sha512": "sha512-L0zNysbUzelk7ODc16TQqrLFMNkZuzOXHWyW3UvIpkoXP4jP7YDKlIVgT07LtB/DV+FNjZGA4wyIZ2OfC6i8VA==", "path": "ollamasharp/4.0.22", "hashPath": "ollamasharp.4.0.22.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-yOcDWx4P/s1I83+7gQlgQLmhny2eNcU0cfo1NBWi+en4EAI38Jau+/neT85gUW6w1s7+FUJc2qNOmmwGLIREqA==", "path": "system.diagnostics.diagnosticsource/9.0.1", "hashPath": "system.diagnostics.diagnosticsource.9.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-iVnDpgYJsRaRFnk77kcLA3+913WfWDtnAKrQl9tQ5ahqKANTaJKmQdsuPWWiAPWE9pk1Kj4Pg9JGXWfFYYyakQ==", "path": "system.diagnostics.eventlog/9.0.1", "hashPath": "system.diagnostics.eventlog.9.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXf5o8eV/gtzDQY4lGROLFMWQvcViKcF8o4Q6KpIOjloAQXrnscQSu6gTxYJMHuNJnh7szIF9AzkaEq+zDLoEg==", "path": "system.io.pipelines/9.0.1", "hashPath": "system.io.pipelines.9.0.1.nupkg.sha512"}, "System.Linq.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "path": "system.linq.async/6.0.1", "hashPath": "system.linq.async.6.0.1.nupkg.sha512"}, "System.Memory.Data/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-S5DSnXpiHQrhO2/hQgJzIA//ywVZ8AWkIZw7U7BCdTJifMZBO1tJM+dJwY0y2ztIVOGcbuGFn/vQ8GSTRg6LLg==", "path": "system.memory.data/9.0.1", "hashPath": "system.memory.data.9.0.1.nupkg.sha512"}, "System.Net.Http.Json/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-F423ZLoJFYg1s6iA+Y7BLflVKjEK5XEA2+Z9CHbxJEUtS3+R5pgnFN499QzriRjYpOu6kS2Crd2YBkOFDHrblg==", "path": "system.net.http.json/8.0.1", "hashPath": "system.net.http.json.8.0.1.nupkg.sha512"}, "System.Numerics.Tensors/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gpl6YWsmR+00SMVM5ItFxVo7fb1Mwmbvl6USewsXZxPH8PKSYLSdq9okeDXdUyH7gWDLJ6GVgJ8dBLfv3fNECQ==", "path": "system.numerics.tensors/9.0.1", "hashPath": "system.numerics.tensors.9.0.1.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XkspqduP2t1e1x2vBUAD/xZ5ZDvmywuUwsmB93MvyQLospJfqtX0GsR/kU0vUL2h4kmvf777z3txV2W4NrQ9Qg==", "path": "system.text.encodings.web/9.0.1", "hashPath": "system.text.encodings.web.9.0.1.nupkg.sha512"}, "System.Text.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-eqWHDZqYPv1PvuvoIIx5pF74plL3iEOZOl/0kQP+Y0TEbtgNnM2W6k8h8EPYs+LTJZsXuWa92n5W5sHTWvE3VA==", "path": "system.text.json/9.0.1", "hashPath": "system.text.json.9.0.1.nupkg.sha512"}}}