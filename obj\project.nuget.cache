{"version": 2, "dgSpecHash": "JznsSuUkFfA=", "success": true, "projectFilePath": "C:\\Codes\\Demo\\Demo.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.27.1\\google.protobuf.3.27.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\9.0.0\\microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.hashcode\\1.1.1\\microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.ai.abstractions\\9.1.0-preview.1.25064.3\\microsoft.extensions.ai.abstractions.9.1.0-preview.1.25064.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.ai.ollama\\9.1.0-preview.1.25064.3\\microsoft.extensions.ai.ollama.9.1.0-preview.1.25064.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.1\\microsoft.extensions.configuration.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.1\\microsoft.extensions.configuration.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.1\\microsoft.extensions.configuration.binder.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\9.0.1\\microsoft.extensions.configuration.commandline.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\9.0.1\\microsoft.extensions.configuration.environmentvariables.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.1\\microsoft.extensions.configuration.fileextensions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.1\\microsoft.extensions.configuration.json.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\9.0.1\\microsoft.extensions.configuration.usersecrets.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.1\\microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.1\\microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\9.0.1\\microsoft.extensions.diagnostics.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\9.0.1\\microsoft.extensions.diagnostics.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.1\\microsoft.extensions.fileproviders.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.1\\microsoft.extensions.fileproviders.physical.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.1\\microsoft.extensions.filesystemglobbing.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\9.0.1\\microsoft.extensions.hosting.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\9.0.1\\microsoft.extensions.hosting.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.1\\microsoft.extensions.logging.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.1\\microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.1\\microsoft.extensions.logging.configuration.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\9.0.1\\microsoft.extensions.logging.console.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\9.0.1\\microsoft.extensions.logging.debug.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\9.0.1\\microsoft.extensions.logging.eventlog.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\9.0.1\\microsoft.extensions.logging.eventsource.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.1\\microsoft.extensions.options.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.1\\microsoft.extensions.options.configurationextensions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.1\\microsoft.extensions.primitives.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.vectordata.abstractions\\9.0.0-preview.1.24523.1\\microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.24523.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.abstractions\\0.96.250120.1\\microsoft.kernelmemory.abstractions.0.96.250120.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.ollama\\0.96.250120.1\\microsoft.kernelmemory.ai.ollama.0.96.250120.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.tiktoken\\0.96.250120.1\\microsoft.kernelmemory.ai.tiktoken.0.96.250120.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers\\1.0.1\\microsoft.ml.tokenizers.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers.data.cl100kbase\\1.0.1\\microsoft.ml.tokenizers.data.cl100kbase.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers.data.o200kbase\\1.0.1\\microsoft.ml.tokenizers.data.o200kbase.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers.data.p50kbase\\1.0.1\\microsoft.ml.tokenizers.data.p50kbase.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.abstractions\\1.33.0\\microsoft.semantickernel.abstractions.1.33.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ollamasharp\\4.0.22\\ollamasharp.4.0.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.1\\system.diagnostics.diagnosticsource.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.1\\system.diagnostics.eventlog.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\9.0.1\\system.io.pipelines.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.async\\6.0.1\\system.linq.async.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\9.0.1\\system.memory.data.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http.json\\8.0.1\\system.net.http.json.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.tensors\\9.0.1\\system.numerics.tensors.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\9.0.1\\system.text.encodings.web.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.1\\system.text.json.9.0.1.nupkg.sha512"], "logs": []}